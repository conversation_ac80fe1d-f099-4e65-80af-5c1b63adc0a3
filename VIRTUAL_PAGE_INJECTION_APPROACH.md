# Virtual Page Injection Approach for Flight Landing Pages

## Overview

This document describes the **Virtual Page Injection** approach for handling flight landing page virtual routes. Instead of trying to modify the rendered output of non-existent pages, this approach creates actual virtual page objects that TYPO3 can process through its normal rendering pipeline.

## The Problem with the Previous Approach

The previous implementation had a fundamental flaw:
- It tried to modify the rendered output of virtual routes that don't exist as real pages
- TYPO3's PageResolver couldn't find pages for virtual routes like `/flights/ber-sof`
- This created a chicken-and-egg problem where content rendering was attempted for non-existent pages
- The `lib.dynamicContent` manipulation couldn't work because there was no valid page context

## The Virtual Page Injection Solution

### Core Concept

Instead of trying to modify existing pages or their output, we **create virtual page objects** that TYPO3 treats as real pages. These virtual pages:

1. **Have valid page UIDs** (negative numbers to avoid conflicts)
2. **Contain merged data** from landing pages and template pages
3. **Include processed placeholder content**
4. **Work with TYPO3's standard rendering pipeline**

### Architecture

The solution uses two middlewares working in sequence:

#### 1. VirtualPageResolverMiddleware
- **Position**: BEFORE PageResolver
- **Purpose**: Detect virtual routes and create virtual page objects
- **Process**:
  1. Detect virtual route patterns (e.g., `/flights/ber-sof`)
  2. Find matching landing page and flight route records
  3. Load template page data
  4. Create a virtual page object by merging landing page + template page data
  5. Process placeholders in page metadata (title, description, etc.)
  6. Inject virtual PageArguments into the request
  7. Store virtual page data for later use

#### 2. VirtualPageContentMiddleware
- **Position**: AFTER TSFE initialization
- **Purpose**: Inject virtual content into TSFE for normal rendering
- **Process**:
  1. Check if current request is for a virtual route
  2. Replace TSFE page data with virtual page data
  3. Load template page content elements
  4. Process placeholders in content elements
  5. Override `lib.dynamicContent` to use virtual content renderer

### Key Components

#### Virtual Page Object Structure
```php
$virtualPage = [
    'uid' => -12345, // Negative UID to avoid conflicts
    'title' => 'Flights from Berlin to Sofia', // Processed placeholders
    'slug' => '/flights/ber-sof',
    'doktype' => 201, // Landing page type
    'is_virtual' => true,
    'template_page_uid' => 123,
    'flight_route_uid' => 456,
    // ... other page fields with processed placeholders
];
```

#### Virtual Content Rendering
- Template page content elements are loaded and processed
- Placeholders are replaced in all text fields
- Content is organized by column position (colPos)
- Standard TYPO3 TypoScript rendering is used

## Benefits of This Approach

### ✅ **Solves the Fundamental Problem**
- Virtual routes now have actual page objects that TYPO3 can process
- No more "rendering content for non-existent pages" issue
- Standard TYPO3 rendering pipeline works normally

### ✅ **Maintains TYPO3 Compatibility**
- Uses standard PageArguments and TSFE structures
- Works with existing TypoScript configurations
- Compatible with TYPO3's caching system
- Supports all standard page features (SEO, redirects, etc.)

### ✅ **Clean Architecture**
- Clear separation of concerns between middlewares
- Reusable virtual page creation logic
- Standard TYPO3 patterns and conventions

### ✅ **Performance Optimized**
- Virtual pages are created only when needed
- Efficient database queries for route matching
- Leverages TYPO3's built-in caching mechanisms

## Implementation Details

### Virtual Page UID Generation
```php
// Generate unique negative UID to avoid conflicts with real pages
$virtualPage['uid'] = -abs(crc32($originalPath . $flightRoute['uid']));
```

### Placeholder Processing
```php
$placeholders = [
    '[_origin_code_]' => $flightRoute['origin_code'],
    '[_destination_code_]' => $flightRoute['destination_code'],
    '[_route_]' => $flightRoute['origin_name'] . ' → ' . $flightRoute['destination_name'],
    // ... more placeholders
];
```

### Content Element Rendering
- Uses TYPO3's standard TypoScript content object rendering
- Maintains compatibility with all content element types
- Supports custom content elements and plugins

## Migration from Previous Approach

### Files to Replace
1. `VirtualRouteDetectionMiddleware.php` → `VirtualPageResolverMiddleware.php`
2. `VirtualRouteEnhancementMiddleware.php` → `VirtualPageContentMiddleware.php`
3. `VirtualRouteContentRenderer.php` → `VirtualContentRenderer.php`
4. Update `Configuration/RequestMiddlewares.php`

### Configuration Changes
- Middleware positioning is simplified
- No complex TypoScript manipulation required
- Standard TYPO3 content rendering configuration works

### Testing the New Approach
1. Clear all caches after implementing changes
2. Test virtual route URLs (e.g., `/flights/ber-sof`)
3. Verify that template page content renders with processed placeholders
4. Check that SEO metadata includes processed placeholders
5. Confirm that standard TYPO3 features work (navigation, caching, etc.)

## Troubleshooting

### Common Issues
1. **Virtual routes return 404**: Check middleware registration and positioning
2. **Content not rendering**: Verify template page content exists and is not hidden
3. **Placeholders not processed**: Check flight route data and placeholder syntax
4. **TypoScript errors**: Ensure site has valid TypoScript template configuration

### Debug Information
- Enable debug logging in middlewares
- Check `$GLOBALS['TYPO3_CONF_VARS']['USER']['virtualRouteData']`
- Verify virtual page object structure
- Test template page content loading

## Future Enhancements

### Potential Improvements
1. **Caching Optimization**: Implement virtual page-specific caching strategies
2. **SEO Enhancement**: Advanced meta tag processing and structured data
3. **Performance Monitoring**: Add metrics for virtual route performance
4. **Content Validation**: Validate template page content and placeholder usage

### Extensibility
- Virtual page creation can be extended for other use cases
- Content processing can be customized per content type
- Additional placeholder types can be easily added
- Integration with external data sources is straightforward

## Conclusion

The Virtual Page Injection approach solves the fundamental architectural problem of the previous implementation by creating actual page objects that TYPO3 can process normally. This results in a more robust, maintainable, and TYPO3-compatible solution for flight landing page virtual routes.
