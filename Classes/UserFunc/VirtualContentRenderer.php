<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\UserFunc;

use TYPO3\CMS\Frontend\ContentObject\ContentObjectRenderer;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * UserFunc to render virtual content for flight landing pages
 * 
 * This replaces lib.dynamicContent for virtual routes and renders
 * template page content with processed placeholders.
 */
class VirtualContentRenderer
{
    /**
     * Main render method called by TypoScript USER object
     */
    public function render(string $content, array $conf, ContentObjectRenderer $cObj): string
    {
        // Check if we have virtual template content
        $virtualContent = $GLOBALS['TYPO3_CONF_VARS']['USER']['virtualTemplateContent'] ?? null;
        
        if (!$virtualContent) {
            // Fallback to normal content rendering
            return $this->renderNormalContent($conf, $cObj);
        }

        // Render virtual content
        return $this->renderVirtualContent($virtualContent, $conf, $cObj);
    }

    /**
     * Render virtual content from template page
     */
    protected function renderVirtualContent(array $virtualContent, array $conf, ContentObjectRenderer $cObj): string
    {
        // Get current column position from register
        $colPos = (int)($cObj->data['colPos'] ?? 0);

        // Filter content elements for current column
        $columnContent = array_filter($virtualContent, function($element) use ($colPos) {
            return (int)($element['colPos'] ?? 0) === $colPos;
        });

        if (empty($columnContent)) {
            return '';
        }

        $renderedContent = '';

        foreach ($columnContent as $contentElement) {
            $renderedContent .= $this->renderContentElement($contentElement, $cObj);
        }

        return $renderedContent;
    }

    /**
     * Render a single content element
     */
    protected function renderContentElement(array $contentElement, ContentObjectRenderer $cObj): string
    {
        // Create a new content object renderer for this element
        $elementCObj = GeneralUtility::makeInstance(ContentObjectRenderer::class, $GLOBALS['TSFE']);
        $elementCObj->start($contentElement, 'tt_content');

        $cType = $contentElement['CType'] ?? 'text';

        // Try to render using TypoScript configuration for this content type
        if (isset($GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType . '.'])) {
            return $elementCObj->cObjGetSingle(
                $GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType] ?? 'TEXT',
                $GLOBALS['TSFE']->tmpl->setup['tt_content.'][$cType . '.'] ?? []
            );
        }

        // Fallback to basic rendering
        return $this->renderBasicContentElement($contentElement);
    }

    /**
     * Render normal TYPO3 content elements (fallback)
     */
    protected function renderNormalContent(array $conf, ContentObjectRenderer $cObj): string
    {
        // Use TYPO3's standard CONTENT object to render normal content
        $contentConf = [
            'table' => 'tt_content',
            'select.' => [
                'includeRecordsWithoutDefaultTranslation' => '1',
                'orderBy' => 'sorting',
                'where' => '{#colPos}={register:colPos}',
                'where.' => [
                    'insertData' => '1'
                ],
                'pidInList.' => [
                    'data' => 'register:pageUid',
                    'override.' => [
                        'data' => 'register:contentFromPid'
                    ]
                ]
            ]
        ];

        return $cObj->cObjGetSingle('CONTENT', $contentConf);
    }

    /**
     * Basic content element rendering (fallback)
     */
    protected function renderBasicContentElement(array $contentElement): string
    {
        $html = '<div class="content-element content-' . htmlspecialchars($contentElement['CType'] ?? 'unknown') . '">';

        if (!empty($contentElement['header'])) {
            $html .= '<h2>' . htmlspecialchars($contentElement['header']) . '</h2>';
        }

        if (!empty($contentElement['subheader'])) {
            $html .= '<h3>' . htmlspecialchars($contentElement['subheader']) . '</h3>';
        }

        if (!empty($contentElement['bodytext'])) {
            // Don't escape HTML in bodytext as it should contain rich content
            $html .= '<div class="bodytext">' . $contentElement['bodytext'] . '</div>';
        }

        $html .= '</div>';

        return $html;
    }
}
