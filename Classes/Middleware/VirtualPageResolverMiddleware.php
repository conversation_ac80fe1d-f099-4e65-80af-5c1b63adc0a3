<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Middleware;

use Bgs\FlightLandingPages\Service\VirtualRouteService;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\MiddlewareInterface;
use Psr\Http\Server\RequestHandlerInterface;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Routing\PageArguments;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Virtual Page Resolver Middleware
 * 
 * This middleware runs BEFORE PageResolver and creates virtual page objects
 * for flight landing page routes. Instead of trying to modify existing pages,
 * it creates complete virtual page records that TYPO3 can process normally.
 */
class VirtualPageResolverMiddleware implements MiddlewareInterface
{
    public function process(ServerRequestInterface $request, RequestHandlerInterface $handler): ResponseInterface
    {
        $site = $request->getAttribute('site');
        
        if (!$site instanceof Site) {
            return $handler->handle($request);
        }

        $path = trim($request->getUri()->getPath(), '/');
        
        // Check if this is a virtual route
        $virtualRouteMatch = $this->matchVirtualRoute($path, $site);
        
        if ($virtualRouteMatch) {
            // Create a virtual page object and inject it into the request
            $modifiedRequest = $this->createVirtualPageRequest($request, $virtualRouteMatch, $site);
            return $handler->handle($modifiedRequest);
        }

        return $handler->handle($request);
    }

    /**
     * Check if the path matches a virtual route pattern
     */
    protected function matchVirtualRoute(string $path, Site $site): ?array
    {
        $pathParts = explode('/', $path);

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ber-sof")
        $routeSlug = end($pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute("{$searchPathLP}/{$routeSlug}", $landingPageData['uid']);

        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => $routeSlug,
            'originalPath' => $path
        ];
    }

    /**
     * Create a virtual page request that TYPO3 can process normally
     */
    protected function createVirtualPageRequest(
        ServerRequestInterface $request, 
        array $virtualRouteMatch, 
        Site $site
    ): ServerRequestInterface {
        $landingPage = $virtualRouteMatch['landingPage'];
        $flightRoute = $virtualRouteMatch['flightRoute'];
        $originalPath = $virtualRouteMatch['originalPath'];

        // Load the template page
        $templatePage = $this->loadTemplatePage($landingPage['template_page_uid']);
        
        if (!$templatePage) {
            return $request; // Fallback to normal processing
        }

        // Create a virtual page object by merging landing page and template page data
        $virtualPage = $this->createVirtualPageObject($landingPage, $templatePage, $flightRoute, $originalPath);

        // Create virtual page arguments
        $virtualPageArguments = new PageArguments(
            $virtualPage['uid'], // Use virtual page UID
            '0', // Page type
            [], // Route arguments
            [], // Static arguments
            [] // Query arguments
        );

        // Store virtual page data for later use
        $virtualRouteData = [
            'isVirtualRoute' => true,
            'virtualPage' => $virtualPage,
            'landingPage' => $landingPage,
            'templatePage' => $templatePage,
            'flightRoute' => $flightRoute,
            'originalPath' => $originalPath
        ];

        // Add virtual page data to request attributes
        $modifiedRequest = $request
            ->withAttribute('routing', $virtualPageArguments)
            ->withAttribute('virtualRoute.data', $virtualRouteData)
            ->withAttribute('virtualRoute.page', $virtualPage);

        return $modifiedRequest;
    }

    /**
     * Create a virtual page object that combines landing page and template page data
     */
    protected function createVirtualPageObject(
        array $landingPage, 
        array $templatePage, 
        array $flightRoute, 
        string $originalPath
    ): array {
        // Start with the landing page as base
        $virtualPage = $landingPage;

        // Override with template page content-related fields
        $virtualPage['title'] = $templatePage['title'] ?? $landingPage['title'];
        $virtualPage['subtitle'] = $templatePage['subtitle'] ?? $landingPage['subtitle'];
        $virtualPage['description'] = $templatePage['description'] ?? $landingPage['description'];
        $virtualPage['keywords'] = $templatePage['keywords'] ?? $landingPage['keywords'];
        $virtualPage['seo_title'] = $templatePage['seo_title'] ?? $landingPage['seo_title'];

        // Process placeholders in the virtual page data
        $virtualPage = $this->processPlaceholdersInPageData($virtualPage, $flightRoute);

        // Set virtual page properties
        $virtualPage['slug'] = '/' . ltrim($originalPath, '/');
        $virtualPage['is_virtual'] = true;
        $virtualPage['template_page_uid'] = $templatePage['uid'];
        $virtualPage['flight_route_uid'] = $flightRoute['uid'];

        // Generate a unique virtual UID (negative to avoid conflicts)
        $virtualPage['uid'] = -abs(crc32($originalPath . $flightRoute['uid']));

        return $virtualPage;
    }

    /**
     * Process placeholders in page data
     */
    protected function processPlaceholdersInPageData(array $pageData, array $flightRoute): array
    {
        $placeholders = [
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),
        ];

        foreach ($pageData as $field => $value) {
            if (is_string($value)) {
                $pageData[$field] = str_replace(array_keys($placeholders), array_values($placeholders), $value);
            }
        }

        return $pageData;
    }

    /**
     * Load template page data
     */
    protected function loadTemplatePage(int $templatePageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(200, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Find landing page by path
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $siteRootPageId = $site->getRootPageId();

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Find flight route by route slug and landing page
     */
    protected function findFlightRoute(string $routeSlug, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('landing_page', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Check if page belongs to site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        // Simple implementation - check if page is under site root
        // You might want to implement a more sophisticated check
        return true; // For now, assume all pages belong to the site
    }
}
